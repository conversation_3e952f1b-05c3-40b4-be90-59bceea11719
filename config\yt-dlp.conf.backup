# yt-dlp 配置文件
# 这个文件包含了 yt-dlp 的默认下载选项
# 更多选项请参考: https://github.com/yt-dlp/yt-dlp#usage-and-options

# 默认下载格式 (可被命令行参数覆盖)
--format=best[height<=1080]

# 输出文件名模板
--output=%(title)s.%(ext)s

# 下载字幕
--write-subs
--sub-langs=zh-CN,en

# 嵌入字幕到视频文件中 (仅支持 mp4/mkv)
--embed-subs

# 下载缩略图
--write-thumbnail

# 嵌入缩略图到音频文件中
--embed-thumbnail

# 添加元数据
--add-metadata

# 代理设置 (如需要，请取消注释并修改)
# --proxy=http://127.0.0.1:7890

# 用户代理
--user-agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

# 重试次数
--retries=3

# 忽略错误继续下载播放列表
--ignore-errors

# 不下载播放列表，只下载指定视频
# --no-playlist
