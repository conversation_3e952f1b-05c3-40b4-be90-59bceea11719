# pyproject.toml

# Build system configuration (standard boilerplate)
[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

# Project metadata and dependencies
[project]
name = "yt-dlp-pro-downloader"
version = "1.0.0"
description = "一个基于yt-dlp的高级媒体下载脚本，支持配置文件和自动Cookie管理。"
authors = [
    { name = "Your Name", email = "<EMAIL>" },
]
license = "MIT"
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "yt-dlp"
]

# (核心修正) 定义一个命令行脚本
[project.scripts]
pro-downloader = "main:main"

# (核心修正) 明确告诉构建工具只打包 main.py 文件
# 我们移除了有问题的 `packages` 键，只使用 `include`
[tool.hatch.build.targets.wheel]
include = ["/main.py"]