#!/usr/bin/env python3
"""
yt-dlp Pro Downloader
一个基于 yt-dlp 的高级媒体下载脚本，支持配置文件和自动Cookie管理。
"""

import argparse
import json
import os
import sys
from pathlib import Path
from urllib.parse import urlparse
from typing import Dict, Optional, Any

try:
    import yt_dlp
except ImportError:
    print("错误: 未找到 yt-dlp 库。请运行以下命令安装:")
    print("uv pip install .")
    print("或者: pip install yt-dlp")
    sys.exit(1)


def get_script_directory() -> Path:
    """获取脚本所在目录的绝对路径"""
    return Path(__file__).parent.absolute()


def ensure_downloads_directory(downloads_path: Path) -> None:
    """确保下载目录存在，如果不存在则创建"""
    try:
        downloads_path.mkdir(parents=True, exist_ok=True)
        print(f"✓ 下载目录已准备: {downloads_path}")
    except Exception as e:
        print(f"错误: 无法创建下载目录 {downloads_path}: {e}")
        sys.exit(1)


def load_yt_dlp_config(config_path: Path) -> Dict[str, Any]:
    """加载 yt-dlp 配置文件"""
    ydl_opts = {}
    
    if config_path.exists():
        ydl_opts['config_location'] = str(config_path)
        print(f"✓ 已加载配置文件: {config_path}")
    else:
        print(f"⚠ 配置文件不存在: {config_path}")
        print("  将使用默认设置")
    
    return ydl_opts


def load_cookie_config(cookie_config_path: Path) -> Dict[str, str]:
    """加载并解析 Cookie 配置文件"""
    cookie_config = {}
    
    if not cookie_config_path.exists():
        print(f"⚠ Cookie 配置文件不存在: {cookie_config_path}")
        return cookie_config
    
    try:
        with open(cookie_config_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        # 过滤掉注释和空值
        cookie_config = {
            domain: cookie for domain, cookie in data.items()
            if not domain.startswith('_') and cookie and isinstance(cookie, str)
        }
        
        if cookie_config:
            print(f"✓ 已加载 Cookie 配置，包含 {len(cookie_config)} 个域名")
        else:
            print("⚠ Cookie 配置文件为空或无有效配置")
            
    except json.JSONDecodeError as e:
        print(f"错误: Cookie 配置文件格式错误: {e}")
    except Exception as e:
        print(f"错误: 无法读取 Cookie 配置文件: {e}")
    
    return cookie_config


def extract_domain(url: str) -> str:
    """从 URL 中提取域名"""
    try:
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        # 移除端口号
        if ':' in domain:
            domain = domain.split(':')[0]
        return domain
    except Exception:
        return ""


def find_matching_cookie(domain: str, cookie_config: Dict[str, str]) -> Optional[str]:
    """智能匹配 Cookie，支持精确匹配和子域名匹配"""
    if not domain or not cookie_config:
        return None

    # 1. 精确匹配
    if domain in cookie_config:
        print(f"✓ 找到精确匹配的 Cookie: {domain}")
        return cookie_config[domain]

    # 2. 子域名匹配
    for config_domain, cookie in cookie_config.items():
        if domain.endswith('.' + config_domain) or domain == config_domain:
            print(f"✓ 找到子域名匹配的 Cookie: {domain} -> {config_domain}")
            return cookie

    print(f"⚠ 未找到匹配的 Cookie: {domain}")
    return None


def is_youtube_url(url: str) -> bool:
    """检测是否为 YouTube URL"""
    try:
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        return 'youtube.com' in domain or 'youtu.be' in domain
    except Exception:
        return False


def is_youtube_shorts(url: str) -> bool:
    """检测是否为 YouTube Shorts URL"""
    try:
        parsed = urlparse(url)
        return 'youtube.com' in parsed.netloc.lower() and '/shorts/' in parsed.path
    except Exception:
        return False


def progress_hook(d: Dict[str, Any]) -> None:
    """下载进度回调函数"""
    if d['status'] == 'downloading':
        if 'total_bytes' in d and d['total_bytes']:
            percent = d['downloaded_bytes'] / d['total_bytes'] * 100
            speed = d.get('speed', 0)
            speed_str = f"{speed/1024/1024:.1f}MB/s" if speed else "未知"
            print(f"\r下载中: {percent:.1f}% - 速度: {speed_str}", end='', flush=True)
        else:
            print(f"\r下载中: {d.get('downloaded_bytes', 0)} 字节", end='', flush=True)
    elif d['status'] == 'finished':
        print(f"\n✓ 下载完成: {d['filename']}")


def main():
    """主函数"""
    # 获取脚本目录和相关路径
    script_dir = get_script_directory()
    config_dir = script_dir / 'config'
    downloads_dir = script_dir / 'downloads'
    yt_dlp_config_path = config_dir / 'yt-dlp.conf'
    cookie_config_path = config_dir / 'cookie_config.json'
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(
        description='yt-dlp Pro Downloader - 高级媒体下载工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py "https://www.youtube.com/watch?v=example"
  python main.py "https://example.com/video" -a
  python main.py "https://example.com/video" -o "D:\\MyVideos"
  python main.py "https://example.com/video" -f "best[height<=720]"
        """
    )
    
    parser.add_argument('url', help='要下载的视频或播放列表的URL')
    parser.add_argument('-o', '--output-path', 
                       default=str(downloads_dir),
                       help='指定保存文件的目录路径')
    parser.add_argument('-a', '--audio-only', 
                       action='store_true',
                       help='只下载音频(mp3)')
    parser.add_argument('-f', '--format',
                       help='手动指定yt-dlp格式代码 (此选项会覆盖所有其他格式设置)')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🎬 yt-dlp Pro Downloader 启动")
    print("=" * 60)
    
    # 确保下载目录存在
    output_path = Path(args.output_path)
    ensure_downloads_directory(output_path)
    
    # 步骤A: 加载通用配置
    ydl_opts = load_yt_dlp_config(yt_dlp_config_path)
    
    # 步骤B: 加载并匹配Cookie
    cookie_config = load_cookie_config(cookie_config_path)
    domain = extract_domain(args.url)
    
    if domain:
        matching_cookie = find_matching_cookie(domain, cookie_config)
        if matching_cookie:
            # 通过 http_headers 添加 Cookie
            if 'http_headers' not in ydl_opts:
                ydl_opts['http_headers'] = {}
            ydl_opts['http_headers']['Cookie'] = matching_cookie
    
    # 步骤C: 处理命令行参数（决定优先级）
    # 设置输出模板
    ydl_opts['outtmpl'] = str(output_path / '%(title)s.%(ext)s')

    # 步骤D: YouTube Shorts 特殊处理
    if is_youtube_shorts(args.url):
        print("🎯 检测到 YouTube Shorts，启用特殊配置...")
        # 为了获得高质量格式，优先使用android客户端（不支持cookies但支持高质量格式）
        # 如果有cookies，我们暂时移除它们以使用android客户端
        if 'http_headers' in ydl_opts and 'Cookie' in ydl_opts['http_headers']:
            print("🔧 为获得高质量格式，临时移除cookies以使用android客户端")
            # 保存cookies以备后用
            saved_cookies = ydl_opts['http_headers']['Cookie']
            del ydl_opts['http_headers']['Cookie']
            if not ydl_opts['http_headers']:
                del ydl_opts['http_headers']

        # 为 YouTube Shorts 添加额外的提取器参数 (强制启用缺少PO Token的格式)
        ydl_opts['extractor_args'] = {
            'youtube': {
                'player_client': ['android'],  # 只使用android客户端获得最佳质量
                'formats': ['missing_pot'],  # 强制启用缺少PO Token的格式
                # 不跳过DASH格式，因为高质量格式就是DASH格式
            }
        }
        # 使用更高质量的格式设置 (优先选择高分辨率DASH格式)
        if not args.format and not args.audio_only:
            ydl_opts['format'] = '137+140/136+140/135+140/134+140/best'  # 明确指定高质量格式组合
            print("✓ YouTube Shorts 优化：使用android客户端获得高质量格式")
    elif is_youtube_url(args.url):
        print("🎯 检测到 YouTube 视频，启用标准配置...")
        # 为普通 YouTube 视频添加提取器参数 (优先使用不需要PO Token的客户端)
        ydl_opts['extractor_args'] = {
            'youtube': {
                'player_client': ['tv', 'web_safari', 'web'],
            }
        }

    # 格式优先级处理
    if args.format:
        # 最高优先级：用户指定的格式
        ydl_opts['format'] = args.format
        print(f"✓ 使用用户指定格式: {args.format}")
    elif args.audio_only:
        # 次优先级：音频模式
        ydl_opts['format'] = 'bestaudio/best'
        ydl_opts['postprocessors'] = [{
            'key': 'FFmpegExtractAudio',
            'preferredcodec': 'mp3',
            'preferredquality': '192',
        }]
        print("✓ 音频模式已启用 (mp3)")
    # 否则使用配置文件中的设置或 YouTube 特殊设置
    
    # 添加进度回调
    ydl_opts['progress_hooks'] = [progress_hook]
    
    print(f"🎯 目标URL: {args.url}")
    print(f"📁 保存路径: {output_path}")
    print("-" * 60)
    
    # 执行下载
    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            ydl.download([args.url])
        
        print("\n" + "=" * 60)
        print("🎉 所有任务完成！")
        print("=" * 60)
        
    except yt_dlp.DownloadError as e:
        error_msg = str(e)
        print(f"\n❌ 下载错误: {e}")

        # YouTube 特殊错误处理
        if is_youtube_url(args.url):
            if "not available on this app" in error_msg or "Watch on the latest version" in error_msg:
                print("\n🔧 YouTube 访问限制解决建议:")
                print("1. 尝试更新 Cookie 配置 (config/cookie_config.json)")
                print("2. 检查网络连接或尝试使用代理")
                print("3. 等待几分钟后重试")
                if is_youtube_shorts(args.url):
                    print("4. YouTube Shorts 可能需要特殊的访问权限")
            elif "Sign in to confirm your age" in error_msg:
                print("\n🔧 年龄限制内容解决建议:")
                print("1. 确保 Cookie 配置包含已登录的 YouTube 账户信息")
                print("2. 在浏览器中登录 YouTube 并更新 Cookie")
            elif "Private video" in error_msg or "Video unavailable" in error_msg:
                print("\n🔧 视频不可用解决建议:")
                print("1. 检查视频链接是否正确")
                print("2. 确认视频是否为私有或已删除")
                print("3. 尝试使用有权限的账户 Cookie")

        sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹ 用户取消下载")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 未知错误: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
