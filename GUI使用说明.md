# yt-dlp GUI 批量下载器使用说明

## 🚀 快速开始

### 启动方法
1. **方法一**: 双击 `启动GUI.bat` 文件
2. **方法二**: 在命令行中运行 `python gui_downloader.py`

### 界面介绍
GUI界面包含以下几个主要区域：

#### 1. URL输入区域
- 在文本框中输入要下载的视频URL
- **支持批量输入**：每行一个URL
- 支持所有yt-dlp兼容的网站（YouTube、B站、抖音等）

#### 2. 下载配置区域
- **输出目录**：选择视频保存位置（默认为downloads文件夹）
- **仅下载音频**：勾选后将下载MP3格式音频
- **格式选择**：可选择视频质量（如720p、1080p等）

#### 3. 控制按钮
- **开始下载**：开始批量下载所有URL
- **停止下载**：停止当前所有下载任务
- **清除列表**：清空任务列表

#### 4. 任务列表
- 显示所有下载任务的状态
- 实时更新下载进度和速度
- 显示文件名和错误信息

#### 5. 日志输出
- 显示详细的下载日志
- 包含错误提示和解决建议

## 📋 使用步骤

### 基本使用
1. 启动GUI应用程序
2. 在URL输入框中粘贴视频链接（每行一个）
3. 选择输出目录（可选）
4. 配置下载选项（可选）
5. 点击"开始下载"按钮
6. 在任务列表中查看下载进度

### 批量下载示例
```
https://www.youtube.com/watch?v=example1
https://www.bilibili.com/video/BV1example2
https://www.douyin.com/video/example3
```

## ⚙️ 高级配置

### Cookie配置
对于需要登录的网站，需要配置Cookie：
1. 编辑 `config/cookie_config.json` 文件
2. 从浏览器开发者工具获取Cookie字符串
3. 按域名添加到配置文件中

### yt-dlp配置
可以编辑 `config/yt-dlp.conf` 文件来设置：
- 默认下载格式
- 字幕下载
- 代理设置
- 其他高级选项

## 🔧 功能特性

### 智能特性
- **自动Cookie匹配**：根据URL域名自动应用对应的Cookie
- **YouTube Shorts优化**：自动检测并优化Shorts视频下载
- **多线程下载**：界面不会冻结，支持实时进度显示
- **错误处理**：详细的错误信息和解决建议

### 支持的网站
- YouTube（包括Shorts）
- Bilibili
- 抖音/TikTok
- Instagram
- Twitter
- 以及所有yt-dlp支持的网站

### 下载选项
- **视频格式**：支持多种分辨率选择
- **音频提取**：可以只下载音频并转换为MP3
- **自定义格式**：支持yt-dlp格式代码
- **批量处理**：一次性处理多个URL

## 🚨 常见问题

### 下载失败
1. **检查网络连接**
2. **验证URL是否正确**
3. **对于需要登录的网站，检查Cookie配置**
4. **查看日志输出中的错误提示**

### YouTube相关问题
- **年龄限制内容**：需要登录账户的Cookie
- **私有视频**：需要有权限的账户Cookie
- **地区限制**：可能需要代理设置

### 性能优化
- 避免同时下载过多任务
- 大文件下载时确保磁盘空间充足
- 网络不稳定时可以降低并发数量

## 📁 文件结构
```
yt-dlp-project/
├── gui_downloader.py      # GUI主程序
├── main.py               # 原命令行版本
├── 启动GUI.bat           # Windows启动脚本
├── config/
│   ├── cookie_config.json # Cookie配置
│   └── yt-dlp.conf       # yt-dlp配置
└── downloads/            # 默认下载目录
```

## 💡 使用技巧

1. **批量下载播放列表**：直接粘贴播放列表URL
2. **音频提取**：勾选"仅下载音频"获取MP3文件
3. **自定义路径**：可以为不同类型内容设置不同的保存目录
4. **格式选择**：根据需要选择合适的视频质量
5. **日志查看**：遇到问题时查看日志输出获取详细信息

## 🔄 更新和维护

- 定期更新yt-dlp库：`pip install --upgrade yt-dlp`
- 更新Cookie配置以保持网站访问权限
- 备份重要的配置文件

---

如有问题或建议，请查看日志输出或联系开发者。
