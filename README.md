# yt-dlp Pro Downloader

这是一个基于 `yt-dlp` 的高级媒体下载脚本，使用 `pyproject.toml`进行现代化项目管理。它拥有清晰的项目结构，并支持从配置文件自动加载 **Cookie 字符串**和**通用下载选项**。

## ✨ 功能特性

- **现代化项目管理**: 使用 `pyproject.toml` 定义项目元数据和依赖，与 `uv`, `pip` 等现代工具无缝集成。
- **结构清晰**：代码、配置、下载内容分离，易于管理。
- **双重配置系统**:
    - `config/yt-dlp.conf`: 用于设置全局下载偏好（如格式、字幕、代理）。
    - `config/cookie_config.json`: 用于自动管理需要登录网站的 **Cookie 字符串**。
- **智能 Cookie**：自动根据链接域名从 JSON 文件加载对应的 Cookie，无需手动干预。
- **优先级明确**：命令行参数会覆盖脚本和配置文件中的设置，方便临时调整。

## 📁 文件结构

```
yt-dlp-downloader/
├── config/
│   ├── cookie_config.json
│   └── yt-dlp.conf
│
├── downloads/
│
├── main.py
├── pyproject.toml               # 项目管理与依赖定义文件
└── README.md
```

## 🚀 快速开始

### 1. 环境设置

a. **安装 Python 和 uv**: 确保你的 Windows 系统已安装 Python (安装时勾选 "Add to PATH") 和 [uv](https://github.com/astral-sh/uv)。

b. **下载项目**: 将本项目所有文件下载或克隆到本地。

c. **进入项目目录**并**安装项目依赖**:
   ```powershell
   # 进入项目目录
   cd path\to\yt-dlp-downloader

   # 创建并激活虚拟环境
   uv venv
   .\.venv\Scripts\activate

   # 安装项目依赖 (核心变化)
   # uv 会自动读取 pyproject.toml 文件并安装其中定义的依赖项
   uv pip install .
   ```

**注意**: 这里的 `.` 代表"当前目录"。这个命令告诉 `uv` 将当前目录作为一个项目进行安装。

### 2. 配置 (按需)

#### a. 设置全局下载偏好 (可选)

- 编辑 `config/yt-dlp.conf` 文件。你可以在里面设置默认的下载格式、字幕语言等。

#### b. 配置网站 Cookie (需要登录时)

- 编辑 `config/cookie_config.json` 文件。
- 从浏览器开发者工具 (`F12`) 中复制目标网站的 **Cookie 字符串**并粘贴进去。

### 3. 开始使用

**确保你的虚拟环境已激活 `(.venv)`**

- **按默认配置下载视频**:
  *(脚本会自动加载 `yt-dlp.conf` 和匹配的 Cookie)*

  ```shell
  python main.py "视频URL"
  ```

- **临时覆盖配置，只下载音频**:
  *(`-a` 参数的优先级高于 `yt-dlp.conf` 中的 `--format` 设置)*

  ```shell
  python main.py "视频URL" -a
  ```

- **下载到指定目录**:

  ```shell
  python main.py "视频URL" -o "D:\MyVideos"
  ```

- **手动指定下载格式**:
  *(`-f` 参数具有最高优先级)*

  ```shell
  python main.py "视频URL" -f "best[height<=720]"
  ```

## 📖 详细说明

### Cookie 配置说明

1. 在浏览器中登录目标网站
2. 按 `F12` 打开开发者工具
3. 转到 `Network` 标签页
4. 刷新页面，找到任意请求
5. 在请求头中找到 `Cookie:` 字段
6. 复制整个 Cookie 字符串到 `config/cookie_config.json` 中对应的域名

### 优先级说明

配置的优先级从高到低：
1. **命令行 `-f` 参数** (最高优先级)
2. **命令行 `-a` 参数** (音频模式)
3. **`config/yt-dlp.conf` 文件设置** (默认配置)

### 支持的网站

理论上支持所有 yt-dlp 支持的网站，包括但不限于：
- YouTube
- Bilibili
- Twitter
- Instagram
- TikTok
- 等等...

## 🔧 故障排除

### 常见问题

1. **"未找到 yt-dlp 库"**
   - 确保已激活虚拟环境：`.\.venv\Scripts\activate`
   - 重新安装依赖：`uv pip install .`

2. **下载失败**
   - 检查网络连接
   - 确认 URL 是否正确
   - 对于需要登录的网站，检查 Cookie 配置

3. **权限错误**
   - 确保对下载目录有写入权限
   - 尝试以管理员身份运行

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！
