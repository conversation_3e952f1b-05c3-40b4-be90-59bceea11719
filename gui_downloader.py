#!/usr/bin/env python3
"""
yt-dlp GUI 批量下载器
基于现有main.py功能的完整GUI应用程序，支持批量下载和实时进度显示。
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import queue
import json
import os
import sys
import time
from pathlib import Path
from urllib.parse import urlparse
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from dataclasses import dataclass

try:
    import yt_dlp
except ImportError:
    messagebox.showerror("错误", "未找到 yt-dlp 库。请运行以下命令安装:\nuv pip install .\n或者: pip install yt-dlp")
    sys.exit(1)


# ==================== 核心功能函数 (来自main.py) ====================

def get_script_directory() -> Path:
    """获取脚本所在目录的绝对路径"""
    return Path(__file__).parent.absolute()


def ensure_downloads_directory(downloads_path: Path) -> None:
    """确保下载目录存在，如果不存在则创建"""
    try:
        downloads_path.mkdir(parents=True, exist_ok=True)
    except Exception as e:
        raise Exception(f"无法创建下载目录 {downloads_path}: {e}")


def load_yt_dlp_config(config_path: Path) -> Dict[str, Any]:
    """加载 yt-dlp 配置文件"""
    ydl_opts = {}
    if config_path.exists():
        ydl_opts['config_location'] = str(config_path)
    return ydl_opts


def load_cookie_config(cookie_config_path: Path) -> Dict[str, str]:
    """加载并解析 Cookie 配置文件"""
    cookie_config = {}
    if not cookie_config_path.exists():
        return cookie_config
    
    try:
        with open(cookie_config_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        # 过滤掉注释和空值
        cookie_config = {
            domain: cookie for domain, cookie in data.items()
            if not domain.startswith('_') and cookie and isinstance(cookie, str)
        }
    except (json.JSONDecodeError, Exception):
        pass
    return cookie_config


def extract_domain(url: str) -> str:
    """从 URL 中提取域名"""
    try:
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        if ':' in domain:
            domain = domain.split(':')[0]
        return domain
    except Exception:
        return ""


def find_matching_cookie(domain: str, cookie_config: Dict[str, str]) -> Optional[str]:
    """智能匹配 Cookie，支持精确匹配和子域名匹配"""
    if not domain or not cookie_config:
        return None
    
    # 精确匹配
    if domain in cookie_config:
        return cookie_config[domain]
    
    # 子域名匹配
    for config_domain, cookie in cookie_config.items():
        if domain.endswith('.' + config_domain) or domain == config_domain:
            return cookie
    return None


def is_youtube_url(url: str) -> bool:
    """检测是否为 YouTube URL"""
    try:
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        return 'youtube.com' in domain or 'youtu.be' in domain
    except Exception:
        return False


def is_youtube_shorts(url: str) -> bool:
    """检测是否为 YouTube Shorts URL"""
    try:
        parsed = urlparse(url)
        return 'youtube.com' in parsed.netloc.lower() and '/shorts/' in parsed.path
    except Exception:
        return False


# ==================== GUI应用程序类 ====================

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "等待中"
    DOWNLOADING = "下载中"
    COMPLETED = "已完成"
    FAILED = "失败"
    CANCELLED = "已取消"


@dataclass
class DownloadTask:
    """下载任务数据类"""
    url: str
    status: TaskStatus = TaskStatus.PENDING
    progress: float = 0.0
    speed: str = "0 B/s"
    filename: str = ""
    error_message: str = ""


class YtDlpGUI:
    """yt-dlp GUI 主应用程序类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("yt-dlp 批量下载器")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)
        
        # 初始化变量
        self.tasks: List[DownloadTask] = []
        self.download_threads: Dict[str, threading.Thread] = {}
        self.is_downloading = False
        self.progress_queue = queue.Queue()
        
        # 加载配置
        self.script_dir = get_script_directory()
        self.config_dir = self.script_dir / 'config'
        self.downloads_dir = self.script_dir / 'downloads'
        self.yt_dlp_config_path = self.config_dir / 'yt-dlp.conf'
        self.cookie_config_path = self.config_dir / 'cookie_config.json'
        self.cookie_config = load_cookie_config(self.cookie_config_path)
        
        # 创建界面
        self.create_widgets()
        self.setup_styles()
        
        # 启动进度更新线程
        self.start_progress_updater()
    
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # URL输入区域
        url_frame = ttk.LabelFrame(main_frame, text="URL 输入 (每行一个)", padding="5")
        url_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N), pady=(0, 10))
        url_frame.columnconfigure(0, weight=1)
        
        self.url_text = scrolledtext.ScrolledText(url_frame, height=6, wrap=tk.WORD)
        self.url_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        url_frame.rowconfigure(0, weight=1)
        
        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="下载配置", padding="5")
        config_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 输出目录
        ttk.Label(config_frame, text="输出目录:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.output_var = tk.StringVar(value=str(self.downloads_dir))
        self.output_entry = ttk.Entry(config_frame, textvariable=self.output_var, width=50)
        self.output_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        ttk.Button(config_frame, text="浏览", command=self.browse_output_dir).grid(row=0, column=2)
        config_frame.columnconfigure(1, weight=1)
        
        # 下载选项
        options_frame = ttk.Frame(config_frame)
        options_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.audio_only_var = tk.BooleanVar()
        ttk.Checkbutton(options_frame, text="仅下载音频 (MP3)", variable=self.audio_only_var).grid(row=0, column=0, sticky=tk.W)
        
        ttk.Label(options_frame, text="格式:").grid(row=0, column=1, sticky=tk.W, padx=(20, 5))
        self.format_var = tk.StringVar()
        format_combo = ttk.Combobox(options_frame, textvariable=self.format_var, width=20)
        format_combo['values'] = ('', 'best', 'best[height<=720]', 'best[height<=1080]', 'worst')
        format_combo.grid(row=0, column=2, sticky=tk.W)
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.start_btn = ttk.Button(button_frame, text="开始下载", command=self.start_downloads)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_btn = ttk.Button(button_frame, text="停止下载", command=self.stop_downloads, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.clear_btn = ttk.Button(button_frame, text="清除列表", command=self.clear_tasks)
        self.clear_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 任务列表和进度显示
        task_frame = ttk.LabelFrame(main_frame, text="下载任务", padding="5")
        task_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        task_frame.columnconfigure(0, weight=1)
        task_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview来显示任务
        columns = ('URL', '状态', '进度', '速度', '文件名')
        self.task_tree = ttk.Treeview(task_frame, columns=columns, show='headings', height=8)
        
        # 设置列标题和宽度
        self.task_tree.heading('URL', text='URL')
        self.task_tree.heading('状态', text='状态')
        self.task_tree.heading('进度', text='进度')
        self.task_tree.heading('速度', text='速度')
        self.task_tree.heading('文件名', text='文件名')
        
        self.task_tree.column('URL', width=200)
        self.task_tree.column('状态', width=80)
        self.task_tree.column('进度', width=80)
        self.task_tree.column('速度', width=100)
        self.task_tree.column('文件名', width=200)
        
        # 添加滚动条
        task_scrollbar = ttk.Scrollbar(task_frame, orient=tk.VERTICAL, command=self.task_tree.yview)
        self.task_tree.configure(yscrollcommand=task_scrollbar.set)
        
        self.task_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        task_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 日志输出区域
        log_frame = ttk.LabelFrame(main_frame, text="日志输出", padding="5")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 调整主框架行权重
        main_frame.rowconfigure(4, weight=1)
    
    def setup_styles(self):
        """设置样式"""
        style = ttk.Style()
        # 可以在这里添加自定义样式
        pass

    def log_message(self, message: str):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(initialdir=self.output_var.get())
        if directory:
            self.output_var.set(directory)

    def parse_urls(self) -> List[str]:
        """解析URL文本框中的URL"""
        text = self.url_text.get("1.0", tk.END).strip()
        urls = [url.strip() for url in text.split('\n') if url.strip()]
        return urls

    def add_task(self, url: str) -> DownloadTask:
        """添加下载任务"""
        task = DownloadTask(url=url)
        self.tasks.append(task)

        # 添加到树形视图
        item_id = self.task_tree.insert('', tk.END, values=(
            url[:50] + '...' if len(url) > 50 else url,
            task.status.value,
            f"{task.progress:.1f}%",
            task.speed,
            task.filename
        ))

        # 存储任务和树形项目的映射
        task.tree_item_id = item_id
        return task

    def update_task_display(self, task: DownloadTask):
        """更新任务显示"""
        if hasattr(task, 'tree_item_id'):
            self.task_tree.item(task.tree_item_id, values=(
                task.url[:50] + '...' if len(task.url) > 50 else task.url,
                task.status.value,
                f"{task.progress:.1f}%",
                task.speed,
                task.filename[:30] + '...' if len(task.filename) > 30 else task.filename
            ))

    def clear_tasks(self):
        """清除所有任务"""
        if self.is_downloading:
            messagebox.showwarning("警告", "请先停止下载再清除任务列表")
            return

        self.tasks.clear()
        for item in self.task_tree.get_children():
            self.task_tree.delete(item)
        self.log_message("已清除所有任务")

    def start_downloads(self):
        """开始批量下载"""
        if self.is_downloading:
            messagebox.showwarning("警告", "下载正在进行中")
            return

        urls = self.parse_urls()
        if not urls:
            messagebox.showwarning("警告", "请输入至少一个URL")
            return

        # 验证输出目录
        output_path = Path(self.output_var.get())
        try:
            ensure_downloads_directory(output_path)
        except Exception as e:
            messagebox.showerror("错误", str(e))
            return

        # 清除现有任务并添加新任务
        self.clear_tasks()
        for url in urls:
            self.add_task(url)

        self.is_downloading = True
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)

        self.log_message(f"开始批量下载，共 {len(urls)} 个任务")

        # 启动下载线程
        download_thread = threading.Thread(target=self.download_worker, daemon=True)
        download_thread.start()

    def stop_downloads(self):
        """停止所有下载"""
        self.is_downloading = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)

        # 更新所有未完成任务的状态
        for task in self.tasks:
            if task.status == TaskStatus.DOWNLOADING:
                task.status = TaskStatus.CANCELLED
                self.update_task_display(task)

        self.log_message("已停止所有下载任务")

    def create_ydl_opts(self, url: str, output_path: Path) -> Dict[str, Any]:
        """创建yt-dlp选项配置"""
        # 加载基础配置
        ydl_opts = load_yt_dlp_config(self.yt_dlp_config_path)

        # 设置输出路径
        ydl_opts['outtmpl'] = str(output_path / '%(title)s.%(ext)s')

        # Cookie配置
        domain = extract_domain(url)
        if domain:
            matching_cookie = find_matching_cookie(domain, self.cookie_config)
            if matching_cookie:
                if 'http_headers' not in ydl_opts:
                    ydl_opts['http_headers'] = {}
                ydl_opts['http_headers']['Cookie'] = matching_cookie

        # YouTube特殊处理
        if is_youtube_shorts(url):
            # 为了获得高质量格式，移除cookies以使用android客户端
            if 'http_headers' in ydl_opts and 'Cookie' in ydl_opts['http_headers']:
                del ydl_opts['http_headers']['Cookie']
                if not ydl_opts['http_headers']:
                    del ydl_opts['http_headers']

            ydl_opts['extractor_args'] = {
                'youtube': {
                    'player_client': ['android'],
                    'formats': ['missing_pot'],  # 修正为列表格式
                    # 不跳过DASH格式，因为高质量格式就是DASH格式
                }
            }
            if not self.format_var.get() and not self.audio_only_var.get():
                ydl_opts['format'] = '137+140/136+140/135+140/134+140/best'  # 明确指定高质量格式组合
        elif is_youtube_url(url):
            ydl_opts['extractor_args'] = {
                'youtube': {
                    'player_client': ['tv', 'web_safari', 'web'],
                }
            }

        # 格式设置
        if self.format_var.get():
            ydl_opts['format'] = self.format_var.get()
        elif self.audio_only_var.get():
            ydl_opts['format'] = 'bestaudio/best'
            ydl_opts['postprocessors'] = [{
                'key': 'FFmpegExtractAudio',
                'preferredcodec': 'mp3',
                'preferredquality': '192',
            }]

        return ydl_opts

    def create_progress_hook(self, task: DownloadTask):
        """创建进度回调函数"""
        def progress_hook(d: Dict[str, Any]):
            if not self.is_downloading:
                return

            try:
                if d['status'] == 'downloading':
                    if 'total_bytes' in d and d['total_bytes']:
                        progress = (d['downloaded_bytes'] / d['total_bytes']) * 100
                        task.progress = progress

                        speed = d.get('speed', 0)
                        if speed:
                            task.speed = f"{speed/1024/1024:.1f}MB/s"
                        else:
                            task.speed = "未知"
                    else:
                        downloaded = d.get('downloaded_bytes', 0)
                        task.speed = f"{downloaded} 字节"

                    task.status = TaskStatus.DOWNLOADING

                    # 通过队列发送更新
                    self.progress_queue.put(('update', task))

                elif d['status'] == 'finished':
                    task.progress = 100.0
                    task.status = TaskStatus.COMPLETED
                    task.filename = Path(d['filename']).name

                    # 通过队列发送更新
                    self.progress_queue.put(('update', task))
                    self.progress_queue.put(('log', f"✓ 下载完成: {task.filename}"))

            except Exception as e:
                self.progress_queue.put(('log', f"进度回调错误: {e}"))

        return progress_hook

    def download_single_task(self, task: DownloadTask, output_path: Path):
        """下载单个任务"""
        try:
            task.status = TaskStatus.DOWNLOADING
            self.progress_queue.put(('update', task))
            self.progress_queue.put(('log', f"开始下载: {task.url}"))

            # 创建yt-dlp配置
            ydl_opts = self.create_ydl_opts(task.url, output_path)
            ydl_opts['progress_hooks'] = [self.create_progress_hook(task)]

            # 执行下载
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([task.url])

            if task.status != TaskStatus.COMPLETED:
                task.status = TaskStatus.COMPLETED
                task.progress = 100.0
                self.progress_queue.put(('update', task))

        except yt_dlp.DownloadError as e:
            error_msg = str(e)
            task.status = TaskStatus.FAILED
            task.error_message = error_msg
            self.progress_queue.put(('update', task))
            self.progress_queue.put(('log', f"❌ 下载失败: {task.url} - {error_msg}"))

            # YouTube特殊错误处理提示
            if is_youtube_url(task.url):
                if "not available on this app" in error_msg or "Watch on the latest version" in error_msg:
                    self.progress_queue.put(('log', "🔧 建议: 更新Cookie配置或检查网络连接"))
                elif "Sign in to confirm your age" in error_msg:
                    self.progress_queue.put(('log', "🔧 建议: 需要登录账户的Cookie"))
                elif "Private video" in error_msg or "Video unavailable" in error_msg:
                    self.progress_queue.put(('log', "🔧 建议: 检查视频链接或权限"))

        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            self.progress_queue.put(('update', task))
            self.progress_queue.put(('log', f"❌ 未知错误: {task.url} - {e}"))

    def download_worker(self):
        """下载工作线程"""
        output_path = Path(self.output_var.get())

        for task in self.tasks:
            if not self.is_downloading:
                break

            # 下载单个任务
            self.download_single_task(task, output_path)

            # 短暂延迟避免过快请求
            time.sleep(0.5)

        # 所有任务完成
        self.progress_queue.put(('batch_complete', None))

    def start_progress_updater(self):
        """启动进度更新器"""
        def update_progress():
            try:
                while True:
                    try:
                        event, data = self.progress_queue.get(timeout=0.1)

                        if event == 'update':
                            self.update_task_display(data)
                        elif event == 'log':
                            self.log_message(data)
                        elif event == 'batch_complete':
                            self.on_batch_complete()

                    except queue.Empty:
                        pass

                    # 继续更新
                    self.root.after(100, update_progress)
                    break

            except Exception as e:
                print(f"进度更新错误: {e}")
                self.root.after(100, update_progress)

        # 启动更新循环
        self.root.after(100, update_progress)

    def on_batch_complete(self):
        """批量下载完成处理"""
        self.is_downloading = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)

        # 统计结果
        completed = sum(1 for task in self.tasks if task.status == TaskStatus.COMPLETED)
        failed = sum(1 for task in self.tasks if task.status == TaskStatus.FAILED)

        self.log_message(f"🎉 批量下载完成! 成功: {completed}, 失败: {failed}")

        if completed > 0:
            messagebox.showinfo("完成", f"批量下载完成!\n成功: {completed} 个\n失败: {failed} 个")

    def run(self):
        """运行GUI应用程序"""
        self.log_message("yt-dlp 批量下载器已启动")
        self.log_message(f"配置目录: {self.config_dir}")
        self.log_message(f"默认下载目录: {self.downloads_dir}")

        if self.cookie_config:
            self.log_message(f"已加载 {len(self.cookie_config)} 个域名的Cookie配置")
        else:
            self.log_message("未找到Cookie配置，某些网站可能需要登录")

        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = YtDlpGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("启动错误", f"应用程序启动失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
