# yt-dlp 配置文件 (备份于执行前)
# 这个文件包含了 yt-dlp 的默认下载选项
# 更多选项请参考: https://github.com/yt-dlp/yt-dlp#usage-and-options

# 默认下载格式 (可被命令行参数覆盖)
--format=best[height<=1080]

# 输出文件名模板
--output=%(title)s.%(ext)s

# 下载字幕
--write-subs
--sub-langs=zh-CN,en

# 嵌入字幕到视频文件中 (仅支持 mp4/mkv)
--embed-subs

# 下载缩略图
--write-thumbnail

# 嵌入缩略图到音频文件中
--embed-thumbnail

# 添加元数据
--add-metadata

# 代理设置 (如需要，请取消注释并修改)
# --proxy=http://127.0.0.1:7890

# 用户代理 (移动端，针对 YouTube Shorts 优化)
--user-agent="Mozilla/5.0 (Linux; Android 13; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36"

# YouTube 提取器参数 (强制使用移动端客户端)
--extractor-args=youtube:player_client=mweb,android

# 额外的 HTTP 头信息 (模拟真实移动端浏览器)
--add-header="Accept:text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8"
--add-header="Accept-Language:zh-CN,zh;q=0.9,en;q=0.8"
--add-header="Accept-Encoding:gzip, deflate, br"
--add-header="DNT:1"
--add-header="Upgrade-Insecure-Requests:1"
--add-header="Sec-Fetch-Dest:document"
--add-header="Sec-Fetch-Mode:navigate"
--add-header="Sec-Fetch-Site:none"

# 重试次数
--retries=3

# 忽略错误继续下载播放列表
--ignore-errors

# 不下载播放列表，只下载指定视频
# --no-playlist
